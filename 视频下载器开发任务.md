# 上下文
文件名：视频下载器开发任务.md
创建于：2025-01-27
创建者：用户/AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
开发一个集成支付系统的视频下载器，包含Python客户端和PHP服务端。支持多种会员卡类型（日卡、周卡、月卡、季卡、年卡），使用支付宝当面付进行支付，具有完整的授权验证和下载管理功能。

# 项目概述
基于课程猫下载器的架构设计，结合支付宝当面付技术，开发一个商业级的视频下载解决方案。采用模块化设计，支持多平台扩展，具备完整的会员权限管理和支付系统。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
## 现有架构分析
### 课程猫下载器架构优势：
1. **模块化设计**：core/、platforms/、browser/等清晰分层
2. **抽象基类**：BaseDownloader提供统一接口
3. **授权系统**：完整的设备绑定和在线验证
4. **平台扩展**：支持动态加载新平台
5. **安全保护**：反调试、文件完整性验证
6. **自动更新**：版本检查和增量更新

### 支付宝当面付技术要点：
1. **RSA2签名**：高安全级别加密验证
2. **异步回调**：支付状态实时通知
3. **二维码支付**：用户扫码完成支付
4. **订单查询**：主动查询支付状态
5. **沙箱测试**：完整的开发测试环境

## 技术栈确认
- **客户端**：Python 3.8+ (tkinter/PyQt界面，requests网络请求)
- **服务端**：PHP 7.4+ (支付宝SDK，MySQL数据库)
- **数据库**：MySQL 8.0+ (用户、订单、授权管理)
- **缓存**：Redis (会话管理、防重放)
- **Web服务器**：Nginx + PHP-FPM

## 核心需求分析
1. **会员系统**：日卡(1天)、周卡(7天)、月卡(30天)、季卡(90天)、年卡(365天)
2. **支付流程**：选择套餐 → 生成订单 → 扫码支付 → 激活会员
3. **权限控制**：基于会员状态的下载权限验证
4. **平台支持**：可插拔的视频平台模块
5. **用户体验**：简洁的界面、清晰的状态提示

# 提议的解决方案 (由 INNOVATE 模式填充)

## 架构方案选择：集成式架构（推荐）

### 整体架构设计
```
┌─────────────────┐    HTTPS/API    ┌─────────────────┐
│   Python客户端   │ ◄──────────────► │   PHP服务端     │
│                 │                 │                 │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │  GUI界面    │ │                 │ │  支付API    │ │
│ │  下载管理   │ │                 │ │  授权验证   │ │
│ │  会员状态   │ │                 │ │  用户管理   │ │
│ └─────────────┘ │                 │ └─────────────┘ │
│ ┌─────────────┐ │                 │ ┌─────────────┐ │
│ │ 平台模块    │ │                 │ │  数据库     │ │
│ │ 授权验证    │ │                 │ │  Redis缓存  │ │
│ │ 文件管理    │ │                 │ │  日志系统   │ │
│ └─────────────┘ │                 │ └─────────────┘ │
└─────────────────┘                 └─────────────────┘
```

### 核心创新点
1. **统一授权机制**：将支付状态与下载权限无缝集成
2. **灵活会员体系**：支持多种时长和权限组合
3. **实时状态同步**：支付成功即时激活权限
4. **平台权限分级**：不同会员等级解锁不同平台

### 会员权限设计（修正版）
| 会员类型 | 有效期 | 功能权限 | 说明 |
|---------|--------|----------|------|
| 免费版   | 永久   | 试用限制 | 仅支持部分平台试用，有下载限制 |
| 日卡     | 1天    | 完整功能 | 解锁全部平台和功能，1天有效期 |
| 周卡     | 7天    | 完整功能 | 解锁全部平台和功能，7天有效期 |
| 月卡     | 30天   | 完整功能 | 解锁全部平台和功能，30天有效期 |
| 季卡     | 90天   | 完整功能 | 解锁全部平台和功能，90天有效期 |
| 年卡     | 365天  | 完整功能 | 解锁全部平台和功能，365天有效期 |

**核心原则**：
- 所有付费会员享受相同的完整功能
- 唯一区别是使用时长
- 价格根据时长递增，长期套餐更优惠

### 设备绑定机制
#### 设备ID生成策略
- **硬件指纹**：基于CPU序列号、主板序列号、硬盘序列号等生成
- **MAC地址**：网卡物理地址作为辅助标识
- **系统信息**：操作系统版本、计算机名等
- **加密算法**：使用SHA256对组合信息进行哈希生成64位唯一ID

#### 设备绑定规则
- **绑定限制**：每个会员默认只能绑定1台设备
- **自动检测**：客户端启动时自动检测设备变化
- **异常处理**：硬件更换导致设备ID变化时的处理流程
- **管理员控制**：只有管理员可以在后台强制解绑设备

#### 安全措施
- **防虚拟机**：检测虚拟机环境，限制在虚拟机中使用
- **防模拟**：检测设备信息是否被人为修改
- **在线验证**：定期与服务端验证设备绑定状态

### 软件更新机制
#### 更新策略
- **自动检查**：程序启动时自动检查更新
- **版本比较**：基于版本号和版本代码进行比较
- **增量更新**：支持差分更新，减少下载量
- **强制更新**：关键安全更新可设置为强制更新

#### 更新流程
1. **版本检查**：客户端向服务端查询最新版本信息
2. **更新提示**：发现新版本时提示用户是否更新
3. **下载更新**：从服务端下载更新包，显示进度
4. **文件验证**：验证下载文件的完整性和签名
5. **应用更新**：备份当前版本，应用新版本文件
6. **重启程序**：更新完成后重启到新版本

#### 安全机制
- **数字签名**：更新包使用数字签名防止篡改
- **哈希验证**：文件完整性校验
- **回滚机制**：更新失败时自动回滚到原版本
- **权限验证**：只有授权用户才能获取更新

### 后台管理系统功能
#### 核心管理功能
1. **用户管理**
   - 查看所有用户列表和详细信息
   - 用户状态管理（激活/禁用）
   - 用户会员状态查看和修改
   - 设备绑定状态查看和强制解绑
   - 用户登录日志和行为分析

2. **订单管理**
   - 订单列表查看和搜索
   - 订单状态管理（待支付/已支付/已取消/已过期）
   - 支付记录查看和对账
   - 退款处理和记录

3. **会员管理**
   - 会员套餐配置（价格、时长调整）
   - 批量会员操作（延期、升级等）
   - 会员统计和分析报表
   - 会员到期提醒设置

4. **平台管理**
   - 支持的视频平台配置
   - 平台状态管理（启用/禁用）
   - 平台下载统计
   - 新平台添加和配置

5. **版本管理**
   - 软件版本发布和管理
   - 更新包上传和配置
   - 用户更新状态监控
   - 强制更新和回滚控制

6. **系统管理**
   - 系统配置参数设置
   - 支付宝配置管理
   - 管理员账号管理
   - 操作日志查看和审计

# 实施计划 (由 PLAN 模式生成)

## 技术架构详细设计

### 1. 目录结构设计
```
video_downloader/
├── client/                          # Python客户端
│   ├── main.py                      # 主程序入口
│   ├── config.json                  # 客户端配置
│   ├── core/                        # 核心模块
│   │   ├── __init__.py
│   │   ├── auth.py                  # 授权验证（扩展支付验证）
│   │   ├── payment.py               # 支付相关功能
│   │   ├── membership.py            # 会员状态管理
│   │   ├── base_downloader.py       # 下载器基类
│   │   ├── display.py               # 界面显示
│   │   ├── utils.py                 # 工具函数
│   │   ├── logger.py                # 日志管理
│   │   └── updater.py               # 自动更新模块
│   ├── platforms/                   # 平台实现
│   │   ├── __init__.py
│   │   └── README.md
│   ├── cli/                         # 命令行界面
│   │   ├── __init__.py
│   │   ├── menu.py                  # 菜单系统
│   │   ├── payment_cli.py           # 支付命令行界面
│   │   └── membership_cli.py        # 会员状态显示
│   └── requirements.txt             # Python依赖
│
├── server/                          # PHP服务端
│   ├── public/                      # Web根目录
│   │   ├── index.php                # API入口文件
│   │   ├── admin/                   # 后台管理系统
│   │   │   ├── index.php            # 后台首页
│   │   │   ├── login.php            # 管理员登录
│   │   │   ├── dashboard.php        # 仪表板
│   │   │   ├── users.php            # 用户管理
│   │   │   ├── orders.php           # 订单管理
│   │   │   ├── memberships.php      # 会员管理
│   │   │   ├── platforms.php        # 平台管理
│   │   │   └── settings.php         # 系统设置
│   │   └── api/                     # API接口
│   │       ├── auth/                # 授权相关API
│   │       ├── payment/             # 支付相关API
│   │       ├── membership/          # 会员相关API
│   │       ├── update/              # 软件更新API
│   │       └── admin/               # 后台管理API
│   │   ├── updates/                 # 更新文件存储
│   │   │   ├── versions/            # 版本文件
│   │   │   └── packages/            # 更新包
│   ├── src/                         # 源代码
│   │   ├── Config/                  # 配置管理
│   │   ├── Models/                  # 数据模型
│   │   ├── Services/                # 业务服务
│   │   ├── Controllers/             # 控制器
│   │   └── Utils/                   # 工具类
│   ├── database/                    # 数据库
│   │   ├── migrations/              # 数据库迁移
│   │   └── seeds/                   # 初始数据
│   ├── vendor/                      # Composer依赖
│   ├── composer.json                # PHP依赖配置
│   └── .env                         # 环境配置
│
└── docs/                            # 文档
    ├── api.md                       # API文档
    ├── deployment.md                # 部署文档
    └── development.md               # 开发文档
```

### 2. 数据库设计
```sql
-- 用户表
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    device_id VARCHAR(64) UNIQUE NOT NULL,    -- 设备唯一标识
    device_name VARCHAR(100),                 -- 设备名称（用户可设置）
    username VARCHAR(50),
    email VARCHAR(100),
    max_devices INT DEFAULT 1,                -- 允许绑定的最大设备数
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'banned') DEFAULT 'active'
);

-- 设备绑定记录表
CREATE TABLE user_devices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    device_id VARCHAR(64) NOT NULL,
    device_name VARCHAR(100),
    device_info JSON,                         -- 设备硬件信息
    first_bind_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'unbound') DEFAULT 'active',
    FOREIGN KEY (user_id) REFERENCES users(id),
    UNIQUE KEY unique_user_device (user_id, device_id)
);

-- 会员套餐表（简化版 - 仅时长区别）
CREATE TABLE membership_plans (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,           -- 日卡、周卡、月卡、季卡、年卡
    duration_days INT NOT NULL,          -- 1、7、30、90、365
    price DECIMAL(10,2) NOT NULL,        -- 对应价格
    description TEXT,                    -- 套餐描述
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE orders (
    id INT PRIMARY KEY AUTO_INCREMENT,
    order_no VARCHAR(32) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'paid', 'cancelled', 'expired') DEFAULT 'pending',
    alipay_trade_no VARCHAR(64),
    qr_code TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_at TIMESTAMP NULL,
    expired_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES membership_plans(id)
);

-- 用户会员状态表
CREATE TABLE user_memberships (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    plan_id INT NOT NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    status ENUM('active', 'expired', 'cancelled') DEFAULT 'active',
    order_id INT,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (plan_id) REFERENCES membership_plans(id),
    FOREIGN KEY (order_id) REFERENCES orders(id)
);

-- 平台配置表（简化版）
CREATE TABLE platforms (
    id INT PRIMARY KEY AUTO_INCREMENT,
    platform_name VARCHAR(50) NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    is_free BOOLEAN DEFAULT FALSE,       -- 是否免费用户可用
    requires_membership BOOLEAN DEFAULT TRUE, -- 是否需要会员
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 管理员表
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role ENUM('super_admin', 'admin', 'operator') DEFAULT 'admin',
    last_login_at TIMESTAMP NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description VARCHAR(255),
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES admins(id)
);

-- 操作日志表
CREATE TABLE admin_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    admin_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES admins(id)
);

-- 软件版本表
CREATE TABLE software_versions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(20) NOT NULL,
    version_code INT NOT NULL,
    release_notes TEXT,
    download_url VARCHAR(255),
    file_size BIGINT,
    file_hash VARCHAR(64),
    is_force_update BOOLEAN DEFAULT FALSE,
    is_beta BOOLEAN DEFAULT FALSE,
    min_compatible_version VARCHAR(20),
    release_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('draft', 'published', 'deprecated') DEFAULT 'draft',
    created_by INT,
    FOREIGN KEY (created_by) REFERENCES admins(id)
);

-- 用户更新记录表
CREATE TABLE user_updates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    from_version VARCHAR(20),
    to_version VARCHAR(20),
    update_status ENUM('checking', 'downloading', 'installing', 'completed', 'failed') DEFAULT 'checking',
    error_message TEXT,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## 实施检查清单：

### 阶段1：基础架构搭建
1. 创建项目目录结构
2. 初始化Git仓库和基础配置文件
3. 设置PHP Composer项目和依赖管理
4. 设置Python虚拟环境和依赖管理
5. 创建数据库和基础表结构
6. 配置开发环境（Nginx、PHP-FPM、MySQL、Redis）

### 阶段2：服务端核心功能
7. 实现数据库连接和基础模型类
8. 创建用户注册和设备绑定API
9. 集成支付宝SDK和配置管理
10. 实现会员套餐管理API
11. 实现订单创建和支付预下单API
12. 实现支付宝异步回调处理
13. 实现会员状态验证和权限检查API
14. 添加API安全验证和签名机制

### 阶段3：客户端核心功能
15. 创建Python项目基础结构
16. 实现设备ID生成和本地存储
17. 实现与服务端的HTTP通信模块
18. 扩展授权验证模块支持会员验证
19. 实现会员状态本地缓存和同步
20. 创建支付相关的业务逻辑模块
21. 实现下载器基类的权限检查集成
22. 实现软件更新检查和下载模块
23. 添加更新文件验证和应用机制

### 阶段4：命令行界面开发
24. 设计命令行菜单系统和交互流程
25. 实现会员状态显示功能
26. 创建套餐选择和购买命令行界面
27. 实现支付二维码文本显示和状态提示
28. 添加支付状态轮询和进度提示
29. 集成下载管理命令行界面
30. 实现设置和配置命令行界面
31. 集成软件更新提示和进度显示

### 阶段5：后台管理系统
32. 设计后台管理系统界面和布局
33. 实现管理员登录和权限验证
34. 创建用户管理功能（查看、编辑、禁用用户）
35. 实现订单管理功能（查看、退款、状态修改）
36. 开发会员管理功能（延期、升级、降级）
37. 创建平台管理功能（添加、编辑、启用/禁用平台）
38. 实现版本管理功能（发布、回滚、强制更新）
39. 实现系统设置和配置管理
40. 添加操作日志和审计功能
41. 创建数据统计和报表功能

### 阶段6：平台集成和测试
42. 创建平台模块的抽象接口
43. 实现一个示例平台模块（用于测试）
44. 集成平台权限验证到下载流程
45. 实现完整的用户注册到下载流程测试
46. 进行支付流程端到端测试
47. 测试后台管理系统各项功能
48. 测试软件更新机制
49. 性能优化和错误处理完善

### 阶段7：安全和部署
50. 实现客户端反调试保护（可选）
51. 添加服务端安全防护措施
52. 完善自动更新安全机制
53. 创建部署脚本和文档
54. 进行安全测试和漏洞扫描
55. 准备生产环境配置

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*开发开始后将在此记录实际的代码实现进度*

# 最终审查 (由 REVIEW 模式填充)
