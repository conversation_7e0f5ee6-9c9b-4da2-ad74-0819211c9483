# 课程猫下载器项目结构与代码分析

## 项目概述

"课程猫下载器"是一个多平台网课视频批量下载工具，支持多种教育平台的课程视频获取与下载。项目采用模块化设计，分为客户端和服务端两部分，实现了授权验证、课程获取、视频下载等核心功能。

## 一、目录结构

```
project/
├── main.py                  # 主程序入口
├── config.json              # 配置文件
├── anti_debug.py            # 反调试保护模块
├── updater.py               # 自动更新模块
├── package_updater.py       # 打包更新工具
├── version.json             # 版本信息文件
├── requirements.txt         # 依赖包列表
├── ffmpeg.exe               # 视频处理工具
│
├── core/                    # 核心代码目录
│   ├── __init__.py          # 包初始化文件
│   ├── auth.py              # 授权验证模块
│   ├── base_downloader.py   # 下载器基类
│   ├── display.py           # 界面显示模块
│   ├── utils.py             # 通用工具函数
│   └── logger.py            # 日志管理模块
│
├── platforms/               # 平台实现目录
│   ├── __init__.py          # 平台导入和聚合
│   ├── README.md            # 平台开发指南
│   ├── acfun/               # A站平台实现
│
├── browser/                 # 浏览器相关文件目录
├── cookies/                 # Cookies存储目录
├── downloads/               # 下载文件存储目录
├── logs/                    # 日志文件目录
│
├── k12.kechengmao.top/      # 服务端代码目录
│   ├── api/                 # API接口目录
│   │   ├── activate.php     # 激活授权API
│   │   ├── verify.php       # 验证授权API
│   │   ├── version.php      # 版本检查API
│   │   └── update_license_platforms.php # 更新授权平台API
│   ├── admin/               # 管理后台
│   ├── config/              # 配置文件
│   ├── includes/            # 公共类库
│   │   └── License.php      # 授权管理类
│   ├── install/             # 安装脚本
│   └── update/              # 更新文件目录
│
└── config/                  # 客户端配置目录
```

## 二、核心文件分析

### 1. 主程序入口 (main.py)

**功能**：
- 整个程序的入口点，协调各模块工作
- 处理用户交互和主要下载流程
- 集成授权验证和平台选择功能

**核心代码逻辑**：
- 初始化各个组件（反调试、授权、显示、平台下载器等）
- 实现用户界面和交互逻辑
- 提供主要的下载流程控制（全课程下载、选定章节下载等）
- 处理异常情况和错误恢复
- 管理程序生命周期（启动、退出等）

**主要方法**：
- `__init__()` - 初始化下载器和组件
- `start()` - 启动程序主流程
- `_check_auth()` - 检查授权状态
- `_process_platform()` - 处理用户选择的平台
- `_process_batch_courses()` - 批量处理多个课程
- `_process_single_course()` - 处理单个课程的下载
- `_download_entire_course()` - 下载整个课程的逻辑
- `_download_selected_chapters()` - 下载选定章节的逻辑

### 2. 授权管理模块 (core/auth.py)

**功能**：
- 管理设备授权和验证逻辑
- 处理激活码验证和状态维护
- 实现在线和离线授权验证

**核心代码逻辑**：
- 生成和验证设备唯一ID
- 管理授权信息的存储和读取
- 实现与授权服务器的通信
- 处理授权状态检查和更新

**主要方法**：
- `get_device_id()` - 生成设备唯一标识
- `get_auth_info()` - 获取授权状态信息
- `is_activated()` - 检查是否已激活
- `activate_license()` - 激活授权码
- `verify_platform()` - 验证特定平台授权
- `verify_online()` - 在线验证授权状态

### 3. 下载器基类 (core/base_downloader.py)

**功能**：
- 定义所有平台下载器必须实现的接口
- 提供通用下载功能的实现
- 管理授权验证和路径创建逻辑

**核心代码逻辑**：
- 定义抽象方法规范各平台实现
- 提供与平台无关的通用功能
- 处理下载路径和目录结构管理

**主要方法**：
- `__init__(cookies=None)` - 初始化下载器和授权
- `_check_authorization()` - 检查平台授权
- `login()` - 登录平台的通用逻辑
- `get_course_list()` - 获取课程列表（抽象方法）
- `get_course_directory(course_id)` - 获取课程目录（抽象方法）
- `download_video(video_info, save_path)` - 下载视频（抽象方法）
- `get_save_path(course_id, video_info, base_path)` - 获取保存路径

### 4. 界面显示模块 (core/display.py)

**功能**：
- 处理命令行界面的显示和交互
- 提供菜单、进度条等用户界面元素
- 管理用户输入和选择逻辑

**核心代码逻辑**：
- 提供各种菜单和选择界面的实现
- 处理用户输入和验证
- 显示下载进度和状态信息
- 提供激活界面和授权管理UI

**主要方法**：
- `show_welcome()` - 显示欢迎界面
- `show_platform_selection()` - 显示平台选择界面
- `show_course_list()` - 显示课程列表
- `show_course_directory()` - 显示课程章节目录
- `show_download_progress()` - 显示下载进度
- `show_activate_license()` - 显示激活授权界面

### 5. 工具函数模块 (core/utils.py)

**功能**：
- 提供通用工具函数和辅助方法
- 处理文件、路径、配置等操作
- 提供数据处理和转换功能

**核心代码逻辑**：
- 实现文件和目录操作的工具方法
- 提供配置文件读写功能
- 实现字符串处理和数据转换
- 提供网络请求和数据解析辅助功能

**主要方法**：
- `create_directory()` - 创建目录
- `sanitize_filename()` - 清理文件名
- `load_config()` - 加载配置
- `save_config()` - 保存配置
- `format_filesize()` - 格式化文件大小
- `get_file_size()` - 获取文件大小

### 6. 日志管理模块 (core/logger.py)

**功能**：
- 管理应用程序的日志记录
- 提供不同级别的日志功能
- 支持文件和控制台日志输出

**核心代码逻辑**：
- 配置和初始化日志系统
- 实现不同日志级别的处理
- 管理日志文件的创建和轮换
- 优化日志格式和内容

**主要方法**：
- `setup_logger()` - 设置日志记录器
- `get_logger()` - 获取日志记录器实例
- `log_exception()` - 记录异常信息
- `rotate_logs()` - 轮换日志文件

### 7. 平台实现文件 (platforms/*/downloader.py)

**功能**：
- 实现特定平台的下载逻辑
- 处理平台特定的登录、获取课程、下载视频等功能
- 处理平台特有的数据格式和API

**核心代码逻辑**：
- 继承BaseDownloader并实现所有抽象方法
- 实现平台特定的登录和身份验证
- 处理平台API请求和响应
- 实现特定格式的视频处理和下载

**主要方法**：
- `login()` - 平台特定的登录逻辑
- `get_course_list()` - 获取平台课程列表
- `get_course_directory()` - 获取平台课程章节结构
- `download_video()` - 下载平台视频文件
- `get_course_info()` - 获取课程信息

### 8. 平台汇总模块 (platforms/__init__.py)

**功能**：
- 导入和聚合所有可用的平台下载器
- 提供统一的接口获取所有平台
- 处理平台导入错误和异常

**核心代码逻辑**：
- 动态导入各平台的下载器类
- 构建平台名称到下载器类的映射
- 处理导入失败的平台
- 提供统一的平台获取接口

**主要方法**：
- `get_platform_downloaders()` - 返回所有可用平台下载器的字典

### 9. 反调试保护模块 (anti_debug.py)

**功能**：
- 防止软件被逆向分析和调试
- 检测调试器和虚拟机环境
- 验证程序完整性和执行环境

**核心代码逻辑**：
- 检测常见调试器特征
- 监控函数执行时间异常
- 检测系统时间被修改的情况
- 验证关键文件的完整性

**主要方法**：
- `is_debugger_present()` - 检测调试器
- `check_execution_time()` - 监控函数执行时间
- `check_time_consistency()` - 检测时间一致性
- `verify_file_integrity()` - 验证文件完整性
- `detect_environment()` - 检测虚拟环境
- `initialize()` - 初始化反调试保护

### 10. 自动更新模块 (updater.py)

**功能**：
- 检查和下载软件更新
- 应用更新并重启程序
- 管理更新文件和备份

**核心代码逻辑**：
- 与更新服务器通信检查版本
- 下载并验证更新包
- 备份当前文件并应用更新
- 处理更新失败和恢复

**主要方法**：
- `check_for_updates()` - 检查更新
- `download_update()` - 下载更新包
- `extract_update()` - 解压更新文件
- `apply_update()` - 应用更新
- `_compare_versions()` - 比较版本号
- `verify_license_online()` - 在线验证授权

### 11. 服务端授权管理 (k12.kechengmao.top/includes/License.php)

**功能**：
- 管理授权码生成和验证
- 处理设备激活和授权检查
- 维护平台权限和黑名单

**核心代码逻辑**：
- 验证设备授权状态
- 处理设备激活请求
- 获取和更新平台权限
- 计算授权到期日期

**主要方法**：
- `verifyDeviceAuth()` - 验证设备授权
- `getLicensePlatforms()` - 获取授权平台列表
- `updateLicensePlatforms()` - 更新平台权限
- `activateDevice()` - 激活设备
- `calculateExpireDate()` - 计算过期日期
- `generateLicense()` - 生成授权码

### 12. 服务端API (k12.kechengmao.top/api/*)

**功能**：
- 提供授权验证和激活的HTTP接口
- 处理版本检查和更新信息
- 响应客户端的授权状态查询

**主要API文件**：
- **activate.php** - 处理授权码激活请求
- **verify.php** - 验证设备授权状态
- **version.php** - 提供版本信息和更新地址
- **update_license_platforms.php** - 更新授权平台权限

## 三、工作流程分析

### 1. 启动与授权验证流程

1. 用户启动`main.py`
2. 程序初始化反调试保护
3. 加载配置和授权信息
4. 检查授权状态：
   - 读取本地授权信息
   - 在线验证授权有效性
   - 显示激活界面（如需要）
5. 显示欢迎界面和平台选择

### 2. 平台选择与登录流程

1. 用户选择平台
2. 程序实例化对应的平台下载器
3. 检查该平台的授权权限
4. 执行平台登录流程：
   - 尝试使用已保存的cookies
   - 如果无效，打开浏览器进行登录
   - 保存获取的cookies

### 3. 课程获取与选择流程

1. 获取用户的课程列表
2. 显示课程选择界面
3. 用户选择单个或多个课程
4. 如果是单个课程：
   - 获取课程章节目录
   - 显示章节选择界面
   - 用户选择下载全部或部分章节
5. 如果是多个课程：
   - 自动批量下载所有课程内容

### 4. 视频下载流程

1. 创建课程目录结构
2. 针对每个选定章节：
   - 获取视频信息
   - 确定保存路径
   - 下载视频文件
   - 处理特殊格式（如M3U8）
   - 显示下载进度
3. 完成后显示下载统计
4. 返回主菜单或退出

### 5. 更新检查流程

1. 程序启动时自动检查更新
2. 与更新服务器通信检查版本
3. 如有更新：
   - 用户选择是否更新
   - 下载更新包
   - 验证更新包完整性
   - 备份当前文件
   - 应用更新并重启

## 四、扩展与定制

### 1. 添加新平台支持

1. 在`platforms`目录创建新平台目录
2. 实现必要的文件：
   - `__init__.py` - 包初始化
   - `downloader.py` - 主下载器实现
   - `auth.py` - 认证逻辑
   - `course.py` - 课程处理逻辑
3. 在`platforms/__init__.py`中注册新平台
4. 在服务端添加平台记录和权限

### 2. 配置文件定制 (config.json)

```json
{
    "download_path": "downloads",
    "max_threads": 3,
    "video_quality": "high",
    "auto_create_dir": true,
    "logging": {
        "logging_enabled": false,
        "log_level": "INFO",
        "console_log_enabled": false,
        "file_log_enabled": true,
        "hide_server_details": true,
        "max_log_files": 20,
        "log_retention_days": 7
    },
    "platforms": {
        "bilibili": {
            "download_rate_limit": 1024,
            "default_quality": "high",
            "cookies_path": "cookies/bilibili_cookies.json"
        }
    }
}
```

### 3. 版本控制 (version.json)

```json
{
  "latest_version": "1.2.5",
  "download_url": "https://kechengmao.top/update/app-v1.2.5.zip",
  "update_message": "重要安全更新，改进授权验证机制",
  "force_update": true,
  "force_downgrade": true,
  "min_compatible_version": "1.2.5",
  "release_date": "2025-4-9"
}
```

## 五、安全与保护机制

### 1. 授权系统设计

- **授权类型**：日卡、月卡、季卡、年卡、永久卡、自定义日期卡
- **设备绑定**：授权码与设备ID绑定
- **平台权限**：不同授权可设置不同平台权限
- **黑名单机制**：支持远程撤销授权
- **在线校验**：定期在线验证授权状态

### 2. 反调试保护

- **调试器检测**：检测常见调试工具
- **执行时间监控**：检测函数执行时间异常
- **时间一致性**：检测系统时间被修改
- **文件完整性**：验证关键文件的完整性
- **环境检测**：检测虚拟机和沙箱环境

## 六、总结

"课程猫下载器"采用模块化设计，具有良好的可扩展性和维护性。核心功能包括多平台支持、授权管理、课程发现、视频下载等。项目集成了反调试保护和自动更新机制，是一个商业级的教育视频下载解决方案。 